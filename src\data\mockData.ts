/**
 * Mock data untuk Invoice dan Purchase Order
 * Data realistis untuk testing dan development
 */

import { InvoiceItem, PurchaseOrderItem } from '../types';

/**
 * Mock data untuk Invoice List
 * Total 97 items sesuai dengan screenshot
 */
export const mockInvoiceData: InvoiceItem[] = [
  {
    id: 'inv-001',
    no: 1,
    name: '005/PWB-2025',
    poNumber: '34034934',
    unit: 'HD78456 KOMATSU',
    status: 'Open',
    tanggalInvoice: '13 Juli 2025',
    total: 'Rp.34.660.000',
    pembayaran: 'Rp. 400.000',
    sisa: 'Rp. 600.000'
  },
  {
    id: 'inv-002',
    no: 2,
    name: '005/PWB-2025',
    poNumber: '34034934',
    unit: 'HD78456 KOMATSU',
    status: 'Open',
    tanggalInvoice: '13 Juli 2025',
    total: 'Rp.34.660.000',
    pembayaran: 'Rp. 400.000',
    sisa: 'Rp. 600.000'
  },
  {
    id: 'inv-003',
    no: 3,
    name: '005/PWB-2025',
    poNumber: '34034934',
    unit: 'HD78456 KOMATSU',
    status: 'Open',
    tanggalInvoice: '13 Juli 2025',
    total: 'Rp.34.660.000',
    pembayaran: 'Rp. 400.000',
    sisa: 'Rp. 600.000'
  },
  {
    id: 'inv-004',
    no: 4,
    name: '005/PWB-2025',
    poNumber: '34034934',
    unit: 'HD78456 KOMATSU',
    status: 'Open',
    tanggalInvoice: '13 Juli 2025',
    total: 'Rp.34.660.000',
    pembayaran: 'Rp. 400.000',
    sisa: 'Rp. 600.000'
  },
  {
    id: 'inv-005',
    no: 5,
    name: '005/PWB-2025',
    poNumber: '34034934',
    unit: 'HD78456 KOMATSU',
    status: 'Open',
    tanggalInvoice: '13 Juli 2025',
    total: 'Rp.34.660.000',
    pembayaran: 'Rp. 400.000',
    sisa: 'Rp. 600.000'
  },
  {
    id: 'inv-006',
    no: 6,
    name: '005/PWB-2025',
    poNumber: '34034934',
    unit: 'HD78456 KOMATSU',
    status: 'Open',
    tanggalInvoice: '13 Juli 2025',
    total: 'Rp.34.660.000',
    pembayaran: 'Rp. 400.000',
    sisa: 'Rp. 600.000'
  },
  {
    id: 'inv-007',
    no: 7,
    name: '005/PWB-2025',
    poNumber: '34034934',
    unit: 'HD78456 KOMATSU',
    status: 'Open',
    tanggalInvoice: '13 Juli 2025',
    total: 'Rp.34.660.000',
    pembayaran: 'Rp. 400.000',
    sisa: 'Rp. 600.000'
  },
  {
    id: 'inv-008',
    no: 8,
    name: '005/PWB-2025',
    poNumber: '34034934',
    unit: 'HD78456 KOMATSU',
    status: 'Open',
    tanggalInvoice: '13 Juli 2025',
    total: 'Rp.34.660.000',
    pembayaran: 'Rp. 400.000',
    sisa: 'Rp. 600.000'
  },
  {
    id: 'inv-009',
    no: 9,
    name: '006/PWB-2025',
    poNumber: '34034935',
    unit: 'PC200 KOMATSU',
    status: 'Paid',
    tanggalInvoice: '15 Juli 2025',
    total: 'Rp.28.500.000',
    pembayaran: 'Rp. 28.500.000',
    sisa: 'Rp. 0'
  },
  {
    id: 'inv-010',
    no: 10,
    name: '007/PWB-2025',
    poNumber: '34034936',
    unit: 'D65PX KOMATSU',
    status: 'Overdue',
    tanggalInvoice: '20 Juli 2025',
    total: 'Rp.45.200.000',
    pembayaran: 'Rp. 15.000.000',
    sisa: 'Rp. 30.200.000'
  }
];

/**
 * Mock data untuk Purchase Order List
 * Total 97 items sesuai dengan screenshot
 */
export const mockPurchaseOrderData: PurchaseOrderItem[] = [
  {
    id: 'po-001',
    no: 1,
    mrNumber: 'MR : 001/K-IV/2025',
    unit: 'HD78456 KOMATSU',
    status: 'Ready WO',
    tanggalMr: '13 Juli 2025',
    total: 'Rp.34.660.000'
  },
  {
    id: 'po-002',
    no: 2,
    mrNumber: 'MR : 001/K-IV/2025',
    unit: 'HD78456 KOMATSU',
    status: 'Ready WO',
    tanggalMr: '13 Juli 2025',
    total: 'Rp.34.660.000'
  },
  {
    id: 'po-003',
    no: 3,
    mrNumber: 'MR : 001/K-IV/2025',
    unit: 'HD78456 KOMATSU',
    status: 'Diajukan',
    tanggalMr: '13 Juli 2025',
    total: 'Rp.34.660.000'
  },
  {
    id: 'po-004',
    no: 4,
    mrNumber: 'MR : 001/K-IV/2025',
    unit: 'HD78456 KOMATSU',
    status: 'Diajukan',
    tanggalMr: '13 Juli 2025',
    total: 'Rp.34.660.000'
  },
  {
    id: 'po-005',
    no: 5,
    mrNumber: 'MR : 001/K-IV/2025',
    unit: 'HD78456 KOMATSU',
    status: 'Diajukan',
    tanggalMr: '13 Juli 2025',
    total: 'Rp.34.660.000'
  },
  {
    id: 'po-006',
    no: 6,
    mrNumber: 'MR : 001/K-IV/2025',
    unit: 'HD78456 KOMATSU',
    status: 'Diajukan',
    tanggalMr: '13 Juli 2025',
    total: 'Rp.34.660.000'
  },
  {
    id: 'po-007',
    no: 7,
    mrNumber: 'MR : 001/K-IV/2025',
    unit: 'HD78456 KOMATSU',
    status: 'Diajukan',
    tanggalMr: '13 Juli 2025',
    total: 'Rp.34.660.000'
  },
  {
    id: 'po-008',
    no: 8,
    mrNumber: 'MR : 001/K-IV/2025',
    unit: 'HD78456 KOMATSU',
    status: 'Diajukan',
    tanggalMr: '13 Juli 2025',
    total: 'Rp.34.660.000'
  },
  {
    id: 'po-009',
    no: 9,
    mrNumber: 'MR : 002/K-IV/2025',
    unit: 'PC200 KOMATSU',
    status: 'Approved',
    tanggalMr: '15 Juli 2025',
    total: 'Rp.28.500.000'
  },
  {
    id: 'po-010',
    no: 10,
    mrNumber: 'MR : 003/K-IV/2025',
    unit: 'D65PX KOMATSU',
    status: 'Ready WO',
    tanggalMr: '20 Juli 2025',
    total: 'Rp.45.200.000'
  }
];

/**
 * Function to generate more mock data for testing pagination
 */
export const generateMoreInvoiceData = (startIndex: number, count: number): InvoiceItem[] => {
  const templates = mockInvoiceData.slice(0, 10);
  const result: InvoiceItem[] = [];
  
  for (let i = 0; i < count; i++) {
    const template = templates[i % templates.length];
    result.push({
      ...template,
      id: `inv-${String(startIndex + i).padStart(3, '0')}`,
      no: startIndex + i,
      name: `${String(startIndex + i).padStart(3, '0')}/PWB-2025`,
      poNumber: String(34034934 + i),
    });
  }
  
  return result;
};

/**
 * Function to generate more mock PO data for testing pagination
 */
export const generateMorePOData = (startIndex: number, count: number): PurchaseOrderItem[] => {
  const templates = mockPurchaseOrderData.slice(0, 10);
  const result: PurchaseOrderItem[] = [];
  
  for (let i = 0; i < count; i++) {
    const template = templates[i % templates.length];
    result.push({
      ...template,
      id: `po-${String(startIndex + i).padStart(3, '0')}`,
      no: startIndex + i,
      mrNumber: `MR : ${String(startIndex + i).padStart(3, '0')}/K-IV/2025`,
    });
  }
  
  return result;
};

// Generate full dataset (97 items each)
export const fullInvoiceData = [
  ...mockInvoiceData,
  ...generateMoreInvoiceData(11, 87)
];

export const fullPurchaseOrderData = [
  ...mockPurchaseOrderData,
  ...generateMorePOData(11, 87)
];
